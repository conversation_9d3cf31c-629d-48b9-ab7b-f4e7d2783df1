/// Permissions utility class for handling user profile type checking
/// This class provides reusable functions to determine user permissions
/// based on their profile types (PMS, EMS, etc.)
class PermissionChecker {
  
  /// Checks user profiles and returns appropriate permission flag
  /// 
  /// Parameters:
  /// - userProfiles: List of user profile objects containing type information
  /// 
  /// Returns:
  /// - 'pmsUser' if user has only PMS type profiles
  /// - 'emsUser' if user has only EMS type profiles  
  /// - 'multiUser' if user has both PMS and EMS type profiles
  /// - 'unknownUser' if no valid types found or profiles is null/empty
  static String permissionChecker(List<dynamic>? userProfiles) {
    // Handle null or empty profiles
    if (userProfiles == null || userProfiles.isEmpty) {
      return 'unknownUser';
    }
    
    // Track which types we've found
    bool hasPMS = false;
    bool hasEMS = false;
    
    // Iterate through all profiles to check their types
    for (var profile in userProfiles) {
      // Ensure profile is a Map and has a 'Type' field
      if (profile is Map<String, dynamic> && profile.containsKey('Type')) {
        String? type = profile['Type']?.toString();
        
        // Check for PMS type
        if (type == 'PMS') {
          hasPMS = true;
        }
        // Check for EMS type
        else if (type == 'EMS') {
          hasEMS = true;
        }
      }
    }
    
    // Determine the appropriate permission flag based on found types
    if (hasPMS && hasEMS) {
      return 'multiUser';  // User has both PMS and EMS profiles
    } else if (hasPMS) {
      return 'pmsUser';    // User has only PMS profiles
    } else if (hasEMS) {
      return 'emsUser';    // User has only EMS profiles
    } else {
      return 'unknownUser'; // No valid types found
    }
  }
  
  /// Convenience method to check if user is PMS type
  static bool isPMSUser(List<dynamic>? userProfiles) {
    return permissionChecker(userProfiles) == 'pmsUser';
  }
  
  /// Convenience method to check if user is EMS type
  static bool isEMSUser(List<dynamic>? userProfiles) {
    return permissionChecker(userProfiles) == 'emsUser';
  }
  
  /// Convenience method to check if user has multiple types
  static bool isMultiUser(List<dynamic>? userProfiles) {
    return permissionChecker(userProfiles) == 'multiUser';
  }
  
  /// Get all unique types from user profiles
  static List<String> getUserTypes(List<dynamic>? userProfiles) {
    if (userProfiles == null || userProfiles.isEmpty) {
      return [];
    }
    
    Set<String> types = {};
    
    for (var profile in userProfiles) {
      if (profile is Map<String, dynamic> && profile.containsKey('Type')) {
        String? type = profile['Type']?.toString();
        if (type != null && type.isNotEmpty) {
          types.add(type);
        }
      }
    }
    
    return types.toList();
  }
}