// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parentEmploymentInfoModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParentEmploymentInfo _$ParentEmploymentInfoFromJson(
  Map<String, dynamic> json,
) => ParentEmploymentInfo(
  id: (json['id'] as num?)?.toInt(),
  parentId: (json['parentId'] as num?)?.toInt(),
  employerTypeId: (json['employerTypeId'] as num?)?.toInt(),
  employerNameId: (json['employerNameId'] as num?)?.toInt(),
  currentEmployerName: json['currentEmployerName'] as String?,
  letterOfAppointmentUpload: json['letterOfAppointmentUpload'] as String?,
  officialEmailId: json['officialEmailId'] as String?,
  isWorking: json['isWorking'] as bool?,
  otp: json['otp'] as String?,
  area: json['area'] as String?,
  documentIssueDate: json['documentIssueDate'] as String?,
  otpExpiry: json['otpExpiry'] as String?,
  place: json['place'] as String?,
  address: json['address'] as String?,
  emirateId: (json['emirateId'] as num?)?.toInt(),
  lattitude: json['lattitude'] as String?,
  longitude: json['longitude'] as String?,
  workLocation: json['workLocation'] as String?,
  nonEmployedStatus: (json['nonEmployedStatus'] as num?)?.toInt(),
  studentDocument: json['studentDocument'] as String?,
  isVerified: json['isVerified'] as bool?,
  createdBy: (json['createdBy'] as num?)?.toInt(),
  createdDate: json['createdDate'] as String?,
  lastUpdatedBy: (json['lastUpdatedBy'] as num?)?.toInt(),
  lastUpdatedDate: json['lastUpdatedDate'] as String?,
  isDeleted: json['isDeleted'] as bool?,
  tempEmployerTypeId: (json['tempEmployerTypeId'] as num?)?.toInt(),
  parent:
      (json['parent'] as List<dynamic>?)
          ?.map((e) => Parent.fromJson(e as Map<String, dynamic>))
          .toList(),
  employerType:
      (json['employerType'] as List<dynamic>?)
          ?.map((e) => EmployerType.fromJson(e as Map<String, dynamic>))
          .toList(),
  emirate:
      (json['emirate'] as List<dynamic>?)
          ?.map((e) => Emirate.fromJson(e as Map<String, dynamic>))
          .toList(),
  employerName:
      (json['employerName'] as List<dynamic>?)
          ?.map((e) => EmployerName.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$ParentEmploymentInfoToJson(
  ParentEmploymentInfo instance,
) => <String, dynamic>{
  'id': instance.id,
  'parentId': instance.parentId,
  'employerTypeId': instance.employerTypeId,
  'employerNameId': instance.employerNameId,
  'currentEmployerName': instance.currentEmployerName,
  'letterOfAppointmentUpload': instance.letterOfAppointmentUpload,
  'officialEmailId': instance.officialEmailId,
  'isWorking': instance.isWorking,
  'otp': instance.otp,
  'area': instance.area,
  'documentIssueDate': instance.documentIssueDate,
  'otpExpiry': instance.otpExpiry,
  'place': instance.place,
  'address': instance.address,
  'emirateId': instance.emirateId,
  'lattitude': instance.lattitude,
  'longitude': instance.longitude,
  'workLocation': instance.workLocation,
  'nonEmployedStatus': instance.nonEmployedStatus,
  'studentDocument': instance.studentDocument,
  'isVerified': instance.isVerified,
  'createdBy': instance.createdBy,
  'createdDate': instance.createdDate,
  'lastUpdatedBy': instance.lastUpdatedBy,
  'lastUpdatedDate': instance.lastUpdatedDate,
  'isDeleted': instance.isDeleted,
  'tempEmployerTypeId': instance.tempEmployerTypeId,
  'parent': instance.parent,
  'employerType': instance.employerType,
  'emirate': instance.emirate,
  'employerName': instance.employerName,
};
