import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:go_router/go_router.dart';

class RoleSelectionScreen extends StatefulWidget {
  @override
  _RoleSelectionScreenState createState() => _RoleSelectionScreenState();
}

class _RoleSelectionScreenState extends State<RoleSelectionScreen> {
  List<bool> selectedRoles = [false, false, false];

  // Helper to get selected role string
  String? getSelectedRole() {
    if (selectedRoles[0]) return 'Parent';
    if (selectedRoles[1]) return 'Vendor';
    if (selectedRoles[2]) return 'Visitor';
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset('assets/images/appbackbutton.svg'),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 20),
            DmSansText(
              'Choose your role(s)!',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.blackColor,
            ),
            SizedBox(height: 4),
            DmSansText(
              'Select the ones that best describe you',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.blackColor,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 30),
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 10,
              runSpacing: 10,
              children: [
                roleCard(
                  0,
                  'I am a parent',
                  'Manage my child\'s activities',
                  'parentlogo',
                ),
                roleCard(
                  1,
                  'I am a vendor',
                  'Provide services here',
                  'vendorlogo',
                ),
                roleCard(
                  2,
                  'I am a visitor',
                  'Just here to explore',
                  'visitorlogo',
                ),
              ],
            ),
            Spacer(),
            ElevatedButton(
              onPressed: selectedRoles.contains(true)
                  ? () async {
                      final selectedRole = getSelectedRole();
                      if (selectedRole != null) {
                        await PreferencesUtils.setSelectedRole(selectedRole);
                        if (selectedRole == 'Parent') {
                          if (context.mounted) {
                            context.go('/profile-dashboard');
                          }
                        } else {
                          if (context.mounted) {
                            context.go('/dashboard');
                          }
                        }
                      }
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedRoles.contains(true)
                    ? AppColors.viewColor
                    : AppColors.lightGreyColor2,
                foregroundColor: AppColors.whiteColor,
                minimumSize: Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: DmSansText(
                "Keep going",
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: selectedRoles.contains(true)
                    ? AppColors.whiteColor
                    : AppColors.lightGreyshade,
              ),
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget roleCard(int index, String title, String subtitle, String imageName) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (index == 2) {
            if (!selectedRoles[2]) {
              selectedRoles = [false, false, true];
            } else {
              selectedRoles[2] = false;
            }
          } else {
            if (!selectedRoles[2]) {
              selectedRoles[index] = !selectedRoles[index];
            }
          }
        });
      },
      child: Container(
        width: MediaQuery.of(context).size.width * 0.4,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.boxshadow.withOpacity(0.25),
              blurRadius: 5,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                CustomPngImage(imageName: imageName, height: 90, width: 90),
                if (selectedRoles[index])
                  Positioned(
                    bottom: -12,
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 28,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 20),
            DmSansText(
              title,
              fontSize: 16.1,
              fontWeight: FontWeight.w700,
              color: AppColors.blackColor,
            ),
            SizedBox(height: 5),
            OpenSansText(
              subtitle,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.blackColor,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
